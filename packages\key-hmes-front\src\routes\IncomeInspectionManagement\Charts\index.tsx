/**
 * 饼图
 * @date 2021/10/13
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2021,Hand
 */
import React, { FC, useRef, useEffect, useState } from 'react';
import * as echarts from 'echarts';
import ReactEchartsCore from 'echarts-for-react/lib/core';
import { Tooltip } from 'choerodon-ui/pro';
import getLangs from '../Langs';
import styles from './index.module.less';
import EmptyDataPic from '../EmptyDataPic';

interface Props {
  [key: string]: any;
}

const Charts: FC<Props> = props => {
  const { data, targetValue, targetData } = props;
  const [clientHeight, setClientHeight] = useState(document.body.clientHeight - 200);
  const chartRef = useRef();
  let total = 0;
  const dataSource = data
    .filter(i => i.targetValue > 0)
    .map(i => {
      total = data[0].sumTargetValue ?? 0;
      return {
        value: i.targetValue,
        name: i.dimName,
        itemStyle: {
          color: i.color,
        },
      };
    });

  useEffect(() => {
    if (chartRef && chartRef.current) {
      // @ts-ignore
      const chart = chartRef!.current!.getEchartsInstance();
      window.addEventListener('resize', () => {
        // eslint-disable-next-line no-unused-expressions
        chart && chart.resize();
        setClientHeight(document.body.clientHeight - 200);
      });
    }
  }, []);

  const currentTarget = targetData.find(i => i.value === targetValue);
  const text = (currentTarget && currentTarget.meaning) || '';

  const renderTooltip = params => {
    const line1 = `<span style="margin:0 4px;color:${params.color};font-weight:bold;padding:0 4px">${text}：  ${params.value}</span><br/>`;
    const icon = `<span style="margin: 8px;height:12px;width:12px;display:inline-block;border-radius:50%;vertical-align:middle;position:relative;top:-1px;background-color:${params.color}"></span>`;
    const percentText = `<span style="color:#1C1C1C;margin: 8px 0">${params.name} ${getLangs(
      'IN_PERCENT'
    )}: ${params.percent}%</span>`;
    return line1 + icon + percentText;
  };

  const subtext =
    targetValue === 'ORIGINAL_COST' ? getLangs('ASSET_TOTAL_VALUE') : getLangs('ASSET_TOTAL_COUNT');

  const getOpts = () => {
    return {
      tooltip: {
        trigger: 'item',
        backgroundColor: '#FFF',
        formatter: params => renderTooltip(params),
        extraCssText: 'box-shadow: 0 2px 10px 5px rgba(124,133,155,0.10);',
      },
      // title: {
      //   text: total,
      //   textStyle: {
      //     color: '#3889ff',
      //     fontWeight: 'bold',
      //     fontSize: 24,
      //   },
      //   subtext,
      //   subtextStyle: {
      //     color: 'black',
      //     fontWeight: 'normal',
      //     fontSize: 16,
      //   },
      //   left: 'center',
      //   top: 'center',
      // },
      series: [
        {
          type: 'pie',
          top: 50,
          radius: ['30%', '60%'],
          startAngle: 225,
          avoidLabelOverlap: true,
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            alignTo: 'labelLine',
            fontSize: 12,
            distanceToLabelLine: 10,
            color: '#5A6677',
          },
          labelLine: {
            length: 20,
            length2: 80,
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 12,
              fontWeight: 'bold',
            },
          },
          data: dataSource,
        },
      ],
    };
  };

  return (
    <>
      <div className={styles['chart-total']}>
        <div className={styles['total-label']}>{subtext}</div>
        <div className={styles['total-number']}>
          <Tooltip title={total}>{total}</Tooltip>
        </div>
      </div>
      <div className={styles['chart-box']}>
        {dataSource.length > 0 ? (
          <ReactEchartsCore
            // @ts-ignore
            ref={chartRef}
            echarts={echarts}
            option={getOpts()}
            notMerge
            style={{ height: clientHeight }}
            lazyUpdate
          />
        ) : (
          <EmptyDataPic />
        )}
      </div>
    </>
  );
};

export default Charts;
