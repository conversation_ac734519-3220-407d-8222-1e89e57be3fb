import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.workshop.emergencyTask';
const tenantId = getCurrentOrganizationId();

const tableDS = () => ({
  autoQuery: false,
  paging: false,
  selection: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/emergency-task/list`,
        method: 'GET',
      };
    },
  },
  dataKey: 'rows',
  totalKey: 'rows',
  autoLocateFirst: false,
  fields: [
    {
      name: 'inspectBusinessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.inspectBusinessTypeDesc`).d('任务类型'),
    },
    {
      name: 'inspectInfoCreationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.inspectInfoCreationDate`).d('报检时间'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.materialName`).d('物料描述'),
    },
    {
      name: 'supplierNameAlt',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.supplierNameAlt`).d('供应商'),
    },
    {
      name: 'siteName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.siteName`).d('基地名称'),
    },
  ],
});

const checkTableDS = () => ({
  autoQuery: false,
  paging: false,
  selection: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/check-supplier/list`,
        method: 'GET',
      };
    },
  },
  dataKey: 'rows',
  totalKey: 'rows',
  autoLocateFirst: false,
  fields: [    
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.supplierName`).d('供应商'),
    },
    {
      name: 'ngQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.ngQty`).d('不良数据'),
    },
    {
      name: 'inspectQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.inspectQty`).d('报检总数'),
    },
    {
      name: 'inspectRate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.inspectRate`).d('批数不良率'),
    },
    {
      name: 'ncReportDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.ncReportDesc`).d('不良描述'),
    },
  ],
});

const ngTableDS = () => ({
  autoQuery: false,
  paging: false,
  selection: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/ng-report/info`,
        method: 'GET',
      };
    },
  },
  dataKey: 'rows',
  totalKey: 'rows',
  autoLocateFirst: false,
  fields: [    
    {
      name: 'actualEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.actualEndTime`).d('发生时间'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.supplierName`).d('供应商'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.materialName`).d('物料描述'),
    },
    {
      name: 'ncReportDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model.emergencyTask.ncReportDesc`).d('不良描述'),
    },
  ],
});

export { tableDS, checkTableDS, ngTableDS };
