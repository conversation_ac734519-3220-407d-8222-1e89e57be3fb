import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { getCurrentOrganizationId } from 'utils/utils';
import * as echarts from 'echarts';
import { debounce } from 'lodash';
import axios from 'axios';
import { BASIC } from '@utils/config';
import DashboardCard from '../DashboardCard.jsx';
// import title from '../../assets/TimelinessRateChartTitle.png';

const tenantId = getCurrentOrganizationId();
// 交验合格率趋势查询URL
const url = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/pass-rate`;

const PassRate = () => {
  const chartRef = useRef(null);
  const [data, setData] = useState<any>([]);
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = useCallback(async () => {
    const res = await axios.get<any, any>(url);
    setData(res);
  }, []);

  const formattedValue = useCallback(
    (v: number) => {
      if (v > 10000) {
        return `${Number((v / 10000).toFixed(2)).toLocaleString('en-US')}
        ${data?.targetCode === 'ORIGINAL_COST' ? '万元' : '万'}`;
      } 
      return `${v.toLocaleString('en-US')}`;
      
    },
    [data],
  );
  const formattedData = useMemo(() => {
    const res = [];
    const dataArr = data?.detailDataVOList ?? [];
    const dataLen = dataArr?.length;

    const otherItem = {
      name: '其他',
      value: 0,
    };
    dataArr.forEach((item, index) => {
      if (index < 5) {
        res.push({
          name: `${item.dimName}`,
          value: item.targetValue,
        });
      } else {
        otherItem.value += item.targetValue;
      }
    });
    if (dataLen > 5) {
      res.push(otherItem);
    }
    return res;
  }, [data]);

  const option = useMemo(() => {
    return {
      title:{        
        top:'2%',
        text: '交验合格率趋势',
        left: 'center',
        textStyle: {
          fontWeight: 'bold', // 加粗
          color: '#00fff4',
        },
      },
      grid: {
        top: '20%',
        left: '4%',
        right: '4%',
        bottom:'2%',
        containLabel: true,
      },
      tooltip: {
        trigger: 'axis',
      },
      xAxis: [
        {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          axisLine: { // 控制轴线样式
            lineStyle: {
              color: '#fff', // 设置轴线颜色
            },
          },
          axisLabel: { // 控制轴标签样式
            textStyle: {
              color: '#fff', // 设置轴标签文字颜色
            },
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisTick: { show: false },// 坐标刻度是否显示
          // splitLine: {show:true}, // 是否显示背景分隔线
          splitLine: { // 分隔线样式
            lineStyle: {
              type: 'dashed', // 改变分隔线样式为虚线 默认是直线
            },
          },
          axisLine: { // 控制轴线样式
            lineStyle: {
              color: '#fff', // 设置轴线颜色
            },
          },
          axisLabel: { // 控制轴标签样式
            textStyle: {
              color: '#fff', // 设置轴标签文字颜色
            },
          },
        },
      ],
      series: [
        {
          name: '柱状图',
          type: 'bar',
          data: [120, 200, 150],
          showBackground: true,
          label: {
            show: true,
            position: 'top',
            color:'#FFF',
          },
          backgroundStyle: {
            color: 'rgba(180, 180, 180, 0.2)',
          },
          itemStyle: {
            color: new echarts.graphic.LinearGradient(
              0, 0, 0, 1, // 渐变方向从上到下
              [
                {offset: 0, color: '#00A2FF'},
                {offset: 1, color: '#00CCD2'},
              ],
            ),
          },
        },
        {
          name: '折线图',
          type: 'line',
          smooth: true,
          data: [0,0,0, 791, 390, 30, 10],
          color:'#00FFF4',
          label: {
            show: true,
            position: 'top',
            color:'#FFF',
          },
          itemStyle: {
            color: new echarts.graphic.LinearGradient(
              0, 0, 0, 1, // 渐变方向从上到下
              [
                {offset: 0, color: '#0E5FFF'},
                {offset: 0.5, color: '#00FFF4'},
                {offset: 1, color: '#0B81FD'},
              ],
            ),
          },
          lineStyle: {
            width: 5,  // 设置线条的粗细为3
          },
        },
      ],
    };
  }, [data]);

  useEffect(() => {
    if (!chartRef.current) return;
    // 初始化echarts实例
    const myChart = echarts.init(chartRef.current);
    myChart.setOption(option);

    const handleResize = debounce(() => {
      myChart.resize();
    }, 200);

    const observer = new ResizeObserver(() => {
      handleResize();
    });
    observer.observe(chartRef.current);

    return () => {
      observer.disconnect();
    };
  }, [option]);

  return (
    <DashboardCard height="100%">
      <div style={{ width: '100%', height: '100%' }}>
        <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
      </div>
    </DashboardCard>
  );
};
export default PassRate;
