import React, { useMemo } from 'react';
import DashboardCard from '../DashboardCard.jsx';
import ScrollBoard from '../ScrollBoard.jsx';
import styles from '../../index.module.less';
// import Top1 from '../../assets/1.png';
// import Top2 from '../../assets/2.png';
// import Top3 from '../../assets/3.png';
// import Top4 from '../../assets/4.png';
// import Top5 from '../../assets/5.png';

const CheckSupplier = ({data}) => {
  const tableData = [];
  if (data.length) {
    data.forEach((val, index) => {
      const {
        supplierName,
        ngQty,
        inspectQty,
        inspectRate,
        ncReportDesc,
      } = val;
      tableData.push([
        (
          `<span className=${styles.checkSupplierText}>NO.${
            index+1
          }</span><span className=${styles.checkSupplierImage}></span>`),
        supplierName,
        ngQty,
        inspectQty,
        inspectRate,
        ncReportDesc,
      ]);
    });
  }
  const config = useMemo(
    () => ({
      header: ['TOP','供应商', '不良数据', '报检总数', '不良率', '不良描述'],
      data: tableData,
      rowNum: 11,
      align: ['center'],
      oddRowBGC: 'rgba(22,66,127,0.3)',
      headerBGC: 'rgb(3, 157, 206,0.3)',
      evenRowBGC: 'rgba(3,28,60, 0.3)',
      headerHeight: 40,
      columnWidth: [100, 100, 100, 120, 100],
    }),
    [tableData],
  );
  return (
    <DashboardCard height="100%" >
      <div style={{ width: '100%', height: '100%' }}>
        <div className={styles['my-scroll-board-title']}>
          交验不合格TOP10供应商
        </div>
        <div className={styles['my-scroll-board-table']}>
          <ScrollBoard config={config} style={{ width: '100%', height: '100%', marginRight: '20px' }} />
        </div>
      </div>
    </DashboardCard>
  );
};

export default CheckSupplier;