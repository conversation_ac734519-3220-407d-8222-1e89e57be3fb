import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { getCurrentOrganizationId } from 'utils/utils';
import * as echarts from 'echarts';
import { Row } from 'choerodon-ui';
import { debounce } from 'lodash';
import axios from 'axios';
import { BASIC } from '@utils/config';
import DashboardCard from '../DashboardCard.jsx';
import PassRate from '../PassRate';
import styles from './index.module.less';
import Process from '../../assets/process.png';
import bao from '../../assets/bao.png';
import mian from '../../assets/mian.png';

const tenantId = getCurrentOrganizationId();
// 报检信息数量查询URL
const url = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/list/info`;
// 检验单数量
const qtyUrl = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/inspect/qty`;

const TotalQty = () => {
  const chartRef1 = useRef(null);
  const chartRef2 = useRef(null);
  const [data, setData] = useState<any>([]);
  const [fillCompletedQty, setFillCompletedQty] = useState<any>([]);
  const [realCompletedQty, setRealCompletedQty] = useState<any>([]);
  const [surplusQty, setSurplusQty] = useState<any>(0);
  useEffect(() => {
    fetchData();
    qtyData();
  }, []);

  const fetchData = useCallback(async () => {
    const res = await axios.get<any, any>(url);
    let newData = {
      inspectionQuantity:20,
      inspectionQuantityRate:20,
      exemptionQuantity:20,
      exemptionQuantityRate:1,
      totalQty:3024,
    }
    if(res.totalQty){
      const rate = Number(res.inspectionQuantity/res.totalQty).toFixed(4) || 0;
      newData = {
        inspectionQuantity:res?.inspectionQuantity||0,
        inspectionQuantityRate:Number(rate),
        exemptionQuantity:res?.exemptionQuantity||0,
        exemptionQuantityRate:1-Number(rate),
        totalQty:res?.totalQty||0,
      }
    }
    setData(newData);
  }, []);

  const qtyData = useCallback(async () => {
    const res = await axios.get<any, any>(qtyUrl);
    if(res){
      const MAX_LENGTH = 6;
      setSurplusQty(res?.surplusQty||0);
      const splitQty = [...`${res?.completedQty}`].map(Number);
      if (splitQty?.length > MAX_LENGTH) {
        setFillCompletedQty([]);
        setRealCompletedQty(Array(MAX_LENGTH).fill(9));
      } 
      else{
        setFillCompletedQty(Array(MAX_LENGTH - splitQty?.length).fill(0));
        setRealCompletedQty(splitQty);
      }      
    }
  }, []);
  
  const option1 = useMemo(() => {
    return {  
      tooltip: {
        trigger: 'item',
      },
      series: [
        {
          name: '',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['60%', '50%'],
          avoidLabelOverlap: false,          
          itemStyle: {
            normal: {
              color(params) {
                // 构建渐变色
                const colorList = [
                  new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0, color: 'rgba(61, 127, 255, 0.5)', // 颜色渐变
                  },{
                    offset: 1, color: 'rgba(102, 255, 255, 0.5)',   // 颜色渐变
                  }]),
                  // ... 更多渐变色
                ];
                return colorList[params.dataIndex % colorList.length];
              },
            },
          },
          label: {
            show: true,
            position: 'center',            
            formatter: `{d|${data.inspectionQuantityRate}%}`,
            // formatter: `{c|${111}} \n {d|工单总数}`,
            // formatter: '{d|中间固定文字}',
            textStyle: {
              rich: {
                d: {
                  fontSize: 12,
                  fontFamily: 'Microsoft YaHei',
                  color: '#7BDBFF',
                },
              },
            },
          },
          data: [
            { value: data.inspectionQuantityRate },
          ],
        },
      ],
    };
  }, [data]);
  const option2 = useMemo(() => {
    return {  
      tooltip: {
        trigger: 'item',
      },
      series: [
        {
          name: '',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['40%', '50%'],
          avoidLabelOverlap: false,          
          itemStyle: {
            normal: {
              color(params) {
                // 构建渐变色
                const colorList = [
                  new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    offset: 0, color: 'rgba(30, 227, 148, 1)', // 颜色渐变
                  }, {
                    offset: 1, color: 'rgba(0, 145, 255, 0.5)',   // 颜色渐变
                  }]),
                  // ... 更多渐变色
                ];
                return colorList[params.dataIndex % colorList.length];
              },
            },
          },
          label: {
            show: true,
            position: 'center',            
            formatter: `{d|${data.exemptionQuantityRate}%}`,
            textStyle: {
              rich: {
                d: {
                  fontSize: 12,
                  fontFamily: 'Microsoft YaHei',
                  color: '#7BDBFF',
                },
              },
            },
          },
          data: [
            { value: data.exemptionQuantityRate },
          ],
        },
      ],
    };
  }, [data]);
  useEffect(() => {
    if (!chartRef1.current) return;
    // 初始化echarts实例
    const myChart1 = echarts.init(chartRef1.current);
    myChart1.setOption(option1);
    const myChart2 = echarts.init(chartRef2.current);
    myChart2.setOption(option2);

    const handleResize = debounce(() => {
      myChart1.resize();
      myChart2.resize();
    }, 200);

    const observer = new ResizeObserver(() => {
      handleResize();
    });
    observer.observe(chartRef1.current);

    return () => {
      observer.disconnect();
    };
  }, [option1, option2]);

  return (
    <DashboardCard style={{ height: '50%'}}>      
      <div style={{ width: '100%', height: '25%', display: 'flex' }} className={styles['item-center-top']}>
        <div style={{ width: '12%', height: '100%' }}>  
          <div ref={chartRef1} style={{ width: '100%', height: '100%' }} />
        </div>
        <div style={{ width: '14%', height: '100%', margin: '2% 0 2% 2%' }}>
          <Row>
            <img src={bao} alt="img" style={{ width: 20, height: 20 }} />
           报检
          </Row>
          <Row style={{color:'#75FEFF',fontWeight: 'bold',fontSize: '14px' }}>
            {data.exemptionQuantity}笔
          </Row>
          <Row style={{fontSize: '10px' }}>
            收货量:{data.totalQty}
          </Row>
        </div>
        <div style={{ width: '50%', height: '85%' }}>
          <div className={styles['space-total-count-material']}>
            {fillCompletedQty.map((i, index) => (
              // eslint-disable-next-line react/no-array-index-key
              <div key={index} className={styles['space-total-count-item-pad-material']}>
                {i}
              </div>
            ))}
            {realCompletedQty.map((i, index) => (
              // eslint-disable-next-line react/no-array-index-key
              <div key={index} className={styles['space-total-count-item-pad-material']}>
                {i}
              </div>
            ))}
            <div className={styles['space-total-count-item-material']}>
              剩余{surplusQty}
            </div>
          </div>
          <div className={styles['space-total-process-material']} >
            <span>今日已完成检验 {data.totalQty}</span>
            <img src={Process} alt="img" />
          </div>
        </div>
        <div style={{ width: '14%', height: '100%', margin: '2% 0 2% 2%'}}>
          <Row>
            <img src={mian} alt="img" style={{ width: 20, height: 20 }} />
            免检
          </Row>
          <Row style={{color:'#75FEFF',fontWeight: 'bold',fontSize: '14px' }}>
            {data.inspectionQuantity}笔
          </Row>
          <Row style={{fontSize: '10px' }}>
            收货量:{data.totalQty}
          </Row>
        </div>
        <div style={{ width: '12%', height: '100%' }}>  
          <div ref={chartRef2} style={{ width: '100%', height: '100%' }} />
        </div>
      </div>
      <div style={{ width: '100%', height: '75%', display: 'flex' }} className={styles['item-center-bottom']}>
        <PassRate />
      </div>
    </DashboardCard>
  );
};
export default TotalQty;
