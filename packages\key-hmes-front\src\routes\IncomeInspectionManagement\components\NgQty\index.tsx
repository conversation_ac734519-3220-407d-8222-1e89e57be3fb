import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { getCurrentOrganizationId } from 'utils/utils';
import * as echarts from 'echarts';
import { debounce } from 'lodash';
import axios from 'axios';
import { BASIC } from '@utils/config';
import DashboardCard from '../DashboardCard.jsx';
import styles from '../../index.module.less';

const tenantId = getCurrentOrganizationId();
// 不合格笔数处置状态
const url = `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/incoming-inspection/ng-qty/info`;

const NgQty = () => {
  const chartRef = useRef(null);
  const [data, setData] = useState<any>([]);
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = useCallback(async () => {
    const res = await axios.get<any, any>(url);
    setData(res);
  }, []);
  
  const data1 = [
    { name: '机房001', value: 1501 },
    { name: '机房002', value: 1305 },
    { name: '机房003', value: 1014 },
    { name: '机房004', value: 1330 },
  ];
  const option = useMemo(() => {
    return {
      title:{        
        top:'1%',
        bottom: '3%',
        text: '不合格笔数处置状态',
        left: 'center',
        textStyle: {
          fontWeight: 'bold', // 加粗
          color: '#00fff4',
        },
      },
      subTitle: [
        {
          // text: `{name|今日不良/笔}\n{val|${formatNumber(totalCount)}}`,
          top: 'center',
          left: 'center',
          textStyle: {
            rich: {
              name: {
                fontSize: 14,
                color: '#666666',
                padding: [10, 0],
              },
              val: {
                fontSize: 32,
                fontWeight: 'bold',
                color: '#333333',
              },
            },
          },
        },
        // {
        //   text: '主机分布',
        //   top: 20,
        //   left: 'center',
        //   textStyle: { fontSize: 14, color: '#666666', fontWeight: 400 },
        // },
      ],
      color:['#FF847F','#fc8251','#5470c6','#9A60B4','#ef6567', '#f9c956','#3BA272'],
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0,0,0,0.9)'},
      series: {
        type: 'pie',
        radius: [50, 100],
        center: ['50%', '55%'],
        left: 'center',
        width: 400,
        itemStyle: { borderColor: '#fff', borderWidth: 1 },
        label: {
          padding: [0, -15],
        },
        labelLine: {
          length: 10,
          length2: 50,
          lineStyle: {
            type: 'dashed', // 设置虚线类型
          },
        },
        data:data1,
      },
    };
  }, [data]);

  useEffect(() => {
    if (!chartRef.current) return;
    // 初始化echarts实例
    const myChart = echarts.init(chartRef.current);
    myChart.setOption(option);

    const handleResize = debounce(() => {
      myChart.resize();
    }, 200);

    const observer = new ResizeObserver(() => {
      handleResize();
    });
    observer.observe(chartRef.current);

    return () => {
      observer.disconnect();
    };
  }, [option]);

  return (
    <DashboardCard style={{ height: '100%' }}>
      <div style={{ width: '100%', height: '100%' }} className={styles['dashboard-right-chart']}>
        <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
      </div>
    </DashboardCard>
  );
};
export default NgQty;
