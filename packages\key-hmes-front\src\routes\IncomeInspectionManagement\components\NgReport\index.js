import React, { useMemo } from 'react';
import DashboardCard from '../DashboardCard.jsx';
import ScrollBoard from '../ScrollBoard.jsx';
import styles from '../../index.module.less';

const NgReport = ({data}) => {
  const tableData = [];
  if (data.length) {
    data.forEach((val) => {
      const {
        supplierName,
        actualEndTime,
        materialName,
        ncReportDesc,
      } = val;
      tableData.push([
        supplierName,
        actualEndTime,
        materialName,
        ncReportDesc,
      ]);
    });
  }
  const config = useMemo(
    () => ({
      header: ['供应商','发生时间',  '物料描述', '不良描述'],
      data: tableData,
      // data: [
      //   ['01', 'S123123123', '物料名称', '到货时间', '采购员', '异常原因'],
      // ],
      rowNum: 7,
      align: ['center'],
      oddRowBGC: 'rgba(22,66,127,0.3)',
      headerBGC: 'rgb(3, 157, 206,0.3)',
      evenRowBGC: 'rgba(3,28,60, 0.3)',
      headerHeight: 40,
      columnWidth: [150, 100, 100, 180],
    }),
    [tableData],
  );
  return (
    <DashboardCard style={{ height: '100%' }} >
      <div style={{ width: '100%', height: '100%' }}>
        <div className={styles['my-scroll-board-title']}>
        不合格信息滚动播报
        </div>
        <div className={styles['my-scroll-board-table']}>
          <ScrollBoard config={config} style={{ width: '100%', height: '100%',margin: '0px 20px' }} />
        </div>
      </div>
    </DashboardCard>
  );
};

export default NgReport;